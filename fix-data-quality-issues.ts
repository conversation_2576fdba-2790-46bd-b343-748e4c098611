import { Client } from 'pg';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.development' });

const createClient = () => new Client({
  connectionString: process.env.POSTGRES_CONNECTION_STRING,
  ssl: { rejectUnauthorized: false }
});

class DataQualityFixer {
  
  async fixAllDataQualityIssues() {
    console.log('🔧 Starting data quality fixes...');
    
    try {
      await this.fixProcedureDescriptions();
      await this.addMissingCarrierInformation();
      await this.createCarrierAliases();
      await this.validateFinalDataQuality();
      
      console.log('✅ All data quality fixes completed!');
      
    } catch (error) {
      console.error('❌ Error fixing data quality:', error);
      throw error;
    }
  }

  private async fixProcedureDescriptions() {
    console.log('🦷 Fixing procedure descriptions...');
    const client = createClient();
    await client.connect();
    
    try {
      // Fix the specific procedures with short descriptions
      const procedureFixes = [
        { code: 'D0220', description: 'Intraoral periapical first radiographic image' },
        { code: 'D0230', description: 'Intraoral periapical each additional radiographic image' }
      ];
      
      for (const fix of procedureFixes) {
        const updateQuery = `
          UPDATE procedures 
          SET description = $1, updated_at = CURRENT_TIMESTAMP
          WHERE procedure_code = $2
        `;
        
        const result = await client.query(updateQuery, [fix.description, fix.code]);
        console.log(`✅ Updated ${fix.code}: ${fix.description}`);
      }
      
    } finally {
      await client.end();
    }
  }

  private async addMissingCarrierInformation() {
    console.log('🏥 Adding missing carrier information...');
    const client = createClient();
    await client.connect();
    
    try {
      // Add known contact information for major carriers that are missing data
      const carrierUpdates = [
        {
          name: 'Delta Dental',
          phone: '**************',
          address: 'P.O. Box 9085, Farmington Hills, MI 48333-9085'
        },
        {
          name: 'Guardian',
          phone: '**************',
          address: 'P.O. Box 981587, El Paso, TX 79998-1587'
        },
        {
          name: 'Humana',
          phone: '**************',
          address: 'P.O. Box 14079, Lexington, KY 40512-4079'
        },
        {
          name: 'Blue Cross Blue Shield Massachusetts',
          phone: '**************',
          address: 'P.O. Box 986030, Boston, MA 02298-6030'
        },
        {
          name: 'GEHA',
          phone: '**************',
          address: 'P.O. Box 21367, Eagan, MN 55121'
        }
      ];
      
      for (const update of carrierUpdates) {
        const updateQuery = `
          UPDATE insurance_carriers
          SET
            phone_number = $1,
            claims_address = $2,
            contact_info = $3::jsonb,
            updated_at = CURRENT_TIMESTAMP
          WHERE carrier_name = $4
          AND (phone_number IS NULL OR phone_number = '')
        `;

        const contactInfo = JSON.stringify({
          phone_number: update.phone,
          claims_address: update.address
        });
        
        const result = await client.query(updateQuery, [update.phone, update.address, contactInfo, update.name]);
        if (result.rowCount && result.rowCount > 0) {
          console.log(`✅ Updated contact info for ${update.name}`);
        }
      }
      
    } finally {
      await client.end();
    }
  }

  private async createCarrierAliases() {
    console.log('🔗 Creating carrier aliases for better matching...');
    const client = createClient();
    await client.connect();
    
    try {
      // Get carrier IDs for alias creation
      const carrierQuery = `
        SELECT id, carrier_name 
        FROM insurance_carriers 
        WHERE carrier_name IN (
          'Blue Cross Blue Shield Massachusetts',
          'Delta Dental',
          'Guardian',
          'Humana',
          'Aetna Dental Plans'
        )
      `;
      
      const carriers = await client.query(carrierQuery);
      const carrierMap = new Map();
      carriers.rows.forEach(row => {
        carrierMap.set(row.carrier_name, row.id);
      });
      
      // Define aliases for major carriers
      const aliases = [
        { carrier: 'Blue Cross Blue Shield Massachusetts', aliases: ['BCBS MA', 'Blue Cross MA', 'BCBSMA'] },
        { carrier: 'Delta Dental', aliases: ['Delta', 'DD', 'Delta Dental Plans'] },
        { carrier: 'Guardian', aliases: ['Guardian Life', 'Guardian Dental'] },
        { carrier: 'Humana', aliases: ['Humana Dental', 'Humana Inc'] },
        { carrier: 'Aetna Dental Plans', aliases: ['Aetna', 'Aetna Dental', 'Aetna Inc'] }
      ];
      
      for (const aliasGroup of aliases) {
        const carrierId = carrierMap.get(aliasGroup.carrier);
        if (!carrierId) continue;
        
        for (const alias of aliasGroup.aliases) {
          // Check if alias already exists
          const existsQuery = `
            SELECT id FROM carrier_aliases 
            WHERE carrier_id = $1 AND alias_name = $2
          `;
          const exists = await client.query(existsQuery, [carrierId, alias]);
          
          if (exists.rows.length === 0) {
            const insertQuery = `
              INSERT INTO carrier_aliases (carrier_id, alias_name)
              VALUES ($1, $2)
            `;
            await client.query(insertQuery, [carrierId, alias]);
            console.log(`✅ Added alias "${alias}" for ${aliasGroup.carrier}`);
          }
        }
      }
      
    } finally {
      await client.end();
    }
  }

  private async validateFinalDataQuality() {
    console.log('🔍 Final data quality validation...');
    const client = createClient();
    await client.connect();
    
    try {
      // Check final statistics
      const statsQuery = `
        SELECT 
          'insurance_carriers' as table_name,
          COUNT(*) as total_records,
          COUNT(*) FILTER (WHERE phone_number IS NOT NULL AND phone_number != '') as with_phone,
          COUNT(*) FILTER (WHERE claims_address IS NOT NULL AND claims_address != '') as with_address
        FROM insurance_carriers
        
        UNION ALL
        
        SELECT 
          'procedures' as table_name,
          COUNT(*) as total_records,
          COUNT(*) FILTER (WHERE description IS NOT NULL AND LENGTH(description) >= 3) as with_description,
          COUNT(*) FILTER (WHERE category IS NOT NULL) as with_category
        FROM procedures
        
        UNION ALL
        
        SELECT 
          'documentation_requirements' as table_name,
          COUNT(*) as total_records,
          COUNT(*) FILTER (WHERE document_type IS NOT NULL) as with_type,
          COUNT(*) FILTER (WHERE format_requirements IS NOT NULL) as with_format
        FROM documentation_requirements
        
        UNION ALL
        
        SELECT 
          'carrier_aliases' as table_name,
          COUNT(*) as total_records,
          COUNT(DISTINCT carrier_id) as unique_carriers,
          0 as placeholder
        FROM carrier_aliases
      `;
      
      const stats = await client.query(statsQuery);
      
      console.log('\n📊 FINAL DATA QUALITY REPORT');
      console.log('=' .repeat(50));
      
      stats.rows.forEach(row => {
        console.log(`\n${row.table_name.toUpperCase()}:`);
        console.log(`  Total records: ${row.total_records}`);
        
        if (row.table_name === 'insurance_carriers') {
          const phonePercent = ((row.with_phone / row.total_records) * 100).toFixed(1);
          const addressPercent = ((row.with_address / row.total_records) * 100).toFixed(1);
          console.log(`  With phone numbers: ${row.with_phone} (${phonePercent}%)`);
          console.log(`  With addresses: ${row.with_address} (${addressPercent}%)`);
        } else if (row.table_name === 'procedures') {
          const descPercent = ((row.with_description / row.total_records) * 100).toFixed(1);
          const catPercent = ((row.with_category / row.total_records) * 100).toFixed(1);
          console.log(`  With descriptions: ${row.with_description} (${descPercent}%)`);
          console.log(`  With categories: ${row.with_category} (${catPercent}%)`);
        } else if (row.table_name === 'documentation_requirements') {
          const typePercent = ((row.with_type / row.total_records) * 100).toFixed(1);
          const formatPercent = ((row.with_format / row.total_records) * 100).toFixed(1);
          console.log(`  With document types: ${row.with_type} (${typePercent}%)`);
          console.log(`  With format requirements: ${row.with_format} (${formatPercent}%)`);
        } else if (row.table_name === 'carrier_aliases') {
          console.log(`  Unique carriers with aliases: ${row.unique_carriers}`);
        }
      });
      
      // Check for remaining issues
      const remainingIssuesQuery = `
        SELECT 
          'Missing phone numbers' as issue,
          COUNT(*) as count
        FROM insurance_carriers 
        WHERE phone_number IS NULL OR phone_number = ''
        
        UNION ALL
        
        SELECT 
          'Short procedure descriptions' as issue,
          COUNT(*) as count
        FROM procedures 
        WHERE description IS NULL OR description = '' OR LENGTH(description) < 3
      `;
      
      const remainingIssues = await client.query(remainingIssuesQuery);
      
      console.log('\n🚨 REMAINING ISSUES:');
      remainingIssues.rows.forEach(issue => {
        if (issue.count > 0) {
          console.log(`  ${issue.issue}: ${issue.count}`);
        }
      });
      
      const totalIssues = remainingIssues.rows.reduce((sum, issue) => sum + parseInt(issue.count), 0);
      if (totalIssues === 0) {
        console.log('  ✅ No remaining data quality issues!');
      }
      
    } finally {
      await client.end();
    }
  }
}

// Main execution
async function main() {
  console.log('🚀 Starting comprehensive data quality fixes...');
  
  const fixer = new DataQualityFixer();
  
  try {
    await fixer.fixAllDataQualityIssues();
    console.log('🎉 Data quality fixes completed successfully!');
  } catch (error) {
    console.error('💥 Data quality fixes failed:', error);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main().catch(console.error);
}

export { DataQualityFixer };
