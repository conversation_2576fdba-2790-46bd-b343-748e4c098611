import { Client } from 'pg';
import OpenAI from 'openai';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.development' });

// Initialize clients
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

const createClient = () => new Client({
  connectionString: process.env.POSTGRES_CONNECTION_STRING,
  ssl: { rejectUnauthorized: false }
});

interface GuidelineData {
  id: number;
  carrier_id: number;
  category: string;
  title: string;
  content: any;
  effective_date?: string;
  expiration_date?: string;
}

interface EmbeddingResult {
  guideline_id: number;
  embedding: number[];
  metadata: any;
  text_content: string;
}

class EmbeddingGenerator {
  private readonly BATCH_SIZE = 10; // Process 10 guidelines at a time
  private readonly RATE_LIMIT_DELAY = 1000; // 1 second between batches
  private readonly EMBEDDING_MODEL = 'text-embedding-3-small';
  private readonly MAX_RETRIES = 3;
  
  private processedCount = 0;
  private errorCount = 0;
  private startTime = Date.now();

  async generateAllEmbeddings() {
    console.log('🚀 Starting OpenAI embedding generation for 2,006 guidelines...');
    
    try {
      // Step 1: Validate prerequisites
      await this.validatePrerequisites();
      
      // Step 2: Get guidelines that need embeddings
      const guidelines = await this.getGuidelinesNeedingEmbeddings();
      
      if (guidelines.length === 0) {
        console.log('✅ All guidelines already have embeddings!');
        return;
      }
      
      console.log(`📊 Found ${guidelines.length} guidelines needing embeddings`);
      
      // Step 3: Process guidelines in batches
      await this.processBatches(guidelines);
      
      // Step 4: Validate results
      await this.validateResults();
      
      console.log('🎉 Embedding generation completed successfully!');
      
    } catch (error) {
      console.error('❌ Error generating embeddings:', error);
      throw error;
    }
  }

  private async validatePrerequisites() {
    console.log('🔍 Validating prerequisites...');
    
    // Check OpenAI API key
    if (!process.env.OPENAI_API_KEY) {
      throw new Error('Missing OPENAI_API_KEY in environment variables');
    }
    
    // Check database connection
    if (!process.env.POSTGRES_CONNECTION_STRING) {
      throw new Error('Missing POSTGRES_CONNECTION_STRING in environment variables');
    }
    
    // Test OpenAI API connection
    try {
      await openai.embeddings.create({
        model: this.EMBEDDING_MODEL,
        input: 'test connection',
      });
      console.log('✅ OpenAI API connection successful');
    } catch (error) {
      throw new Error(`OpenAI API connection failed: ${error}`);
    }
    
    // Test database connection
    const client = createClient();
    try {
      await client.connect();
      await client.query('SELECT 1');
      console.log('✅ Database connection successful');
    } finally {
      await client.end();
    }
  }

  private async getGuidelinesNeedingEmbeddings(): Promise<GuidelineData[]> {
    console.log('📋 Fetching guidelines that need embeddings...');
    
    const client = createClient();
    await client.connect();
    
    try {
      const query = `
        SELECT g.id, g.carrier_id, g.category, g.title, g.content, g.effective_date, g.expiration_date
        FROM guidelines g
        LEFT JOIN embeddings e ON e.content_type = 'guideline' AND e.content_id = g.id
        WHERE e.id IS NULL
        ORDER BY g.id
      `;
      
      const result = await client.query(query);
      console.log(`📊 Found ${result.rows.length} guidelines without embeddings`);
      
      return result.rows;
      
    } finally {
      await client.end();
    }
  }

  private async processBatches(guidelines: GuidelineData[]) {
    console.log(`🔄 Processing ${guidelines.length} guidelines in batches of ${this.BATCH_SIZE}...`);
    
    const totalBatches = Math.ceil(guidelines.length / this.BATCH_SIZE);
    
    for (let i = 0; i < guidelines.length; i += this.BATCH_SIZE) {
      const batch = guidelines.slice(i, i + this.BATCH_SIZE);
      const batchNumber = Math.floor(i / this.BATCH_SIZE) + 1;
      
      console.log(`\n📦 Processing batch ${batchNumber}/${totalBatches} (${batch.length} guidelines)...`);
      
      try {
        await this.processBatch(batch);
        
        // Rate limiting - wait between batches
        if (i + this.BATCH_SIZE < guidelines.length) {
          console.log(`⏳ Waiting ${this.RATE_LIMIT_DELAY}ms for rate limiting...`);
          await this.sleep(this.RATE_LIMIT_DELAY);
        }
        
      } catch (error) {
        console.error(`❌ Error processing batch ${batchNumber}:`, error);
        this.errorCount += batch.length;
        
        // Continue with next batch instead of failing completely
        console.log('⚠️  Continuing with next batch...');
      }
      
      // Progress update
      const progress = ((batchNumber / totalBatches) * 100).toFixed(1);
      const elapsed = ((Date.now() - this.startTime) / 1000).toFixed(1);
      console.log(`📈 Progress: ${progress}% | Processed: ${this.processedCount} | Errors: ${this.errorCount} | Time: ${elapsed}s`);
    }
  }

  private async processBatch(guidelines: GuidelineData[]) {
    // Extract text content from guidelines
    const textContents = guidelines.map(g => this.extractTextContent(g));
    
    // Generate embeddings for the batch
    const embeddings = await this.generateEmbeddingsBatch(textContents);
    
    // Prepare embedding results
    const embeddingResults: EmbeddingResult[] = guidelines.map((guideline, index) => ({
      guideline_id: guideline.id,
      embedding: embeddings[index],
      metadata: {
        carrier_id: guideline.carrier_id,
        category: guideline.category,
        title: guideline.title,
        effective_date: guideline.effective_date,
        expiration_date: guideline.expiration_date,
        text_length: textContents[index].length,
        generated_at: new Date().toISOString()
      },
      text_content: textContents[index]
    }));
    
    // Store embeddings in database
    await this.storeEmbeddings(embeddingResults);
    
    this.processedCount += guidelines.length;
  }

  private extractTextContent(guideline: GuidelineData): string {
    try {
      // Handle different content formats
      if (typeof guideline.content === 'string') {
        return guideline.content;
      }
      
      if (typeof guideline.content === 'object' && guideline.content !== null) {
        // If content is JSON with a 'content' field
        if (guideline.content.content) {
          return guideline.content.content;
        }
        
        // If content is JSON, stringify it
        return JSON.stringify(guideline.content);
      }
      
      // Fallback to title if content is not available
      return guideline.title || '';
      
    } catch (error) {
      console.warn(`⚠️  Error extracting content for guideline ${guideline.id}:`, error);
      return guideline.title || '';
    }
  }

  private async generateEmbeddingsBatch(texts: string[]): Promise<number[][]> {
    let retries = 0;
    
    while (retries < this.MAX_RETRIES) {
      try {
        const response = await openai.embeddings.create({
          model: this.EMBEDDING_MODEL,
          input: texts,
        });
        
        return response.data.map(item => item.embedding);
        
      } catch (error: any) {
        retries++;
        console.warn(`⚠️  OpenAI API error (attempt ${retries}/${this.MAX_RETRIES}):`, error.message);
        
        if (retries >= this.MAX_RETRIES) {
          throw error;
        }
        
        // Exponential backoff
        const delay = Math.pow(2, retries) * 1000;
        console.log(`⏳ Retrying in ${delay}ms...`);
        await this.sleep(delay);
      }
    }
    
    throw new Error('Max retries exceeded');
  }

  private async storeEmbeddings(results: EmbeddingResult[]) {
    const client = createClient();
    await client.connect();
    
    try {
      // Prepare batch insert
      const values = results.map(result => [
        'guideline',
        result.guideline_id,
        JSON.stringify(result.embedding),
        JSON.stringify(result.metadata)
      ]);
      
      const placeholders = values.map((_, idx) => 
        `($${idx * 4 + 1}, $${idx * 4 + 2}, $${idx * 4 + 3}::vector, $${idx * 4 + 4}::jsonb)`
      ).join(', ');
      
      const query = `
        INSERT INTO embeddings (content_type, content_id, embedding, metadata)
        VALUES ${placeholders}
      `;
      
      const flatValues = values.flat();
      await client.query(query, flatValues);
      
      console.log(`✅ Stored ${results.length} embeddings in database`);
      
    } finally {
      await client.end();
    }
  }

  private async validateResults() {
    console.log('🔍 Validating embedding generation results...');
    
    const client = createClient();
    await client.connect();
    
    try {
      const statsQuery = `
        SELECT 
          COUNT(*) as total_guidelines,
          COUNT(e.id) as guidelines_with_embeddings,
          COUNT(*) - COUNT(e.id) as missing_embeddings
        FROM guidelines g
        LEFT JOIN embeddings e ON e.content_type = 'guideline' AND e.content_id = g.id
      `;
      
      const result = await client.query(statsQuery);
      const stats = result.rows[0];
      
      console.log('\n📊 EMBEDDING GENERATION RESULTS:');
      console.log('=' .repeat(40));
      console.log(`Total guidelines: ${stats.total_guidelines}`);
      console.log(`Guidelines with embeddings: ${stats.guidelines_with_embeddings}`);
      console.log(`Missing embeddings: ${stats.missing_embeddings}`);
      
      const completionRate = ((stats.guidelines_with_embeddings / stats.total_guidelines) * 100).toFixed(1);
      console.log(`Completion rate: ${completionRate}%`);
      
      if (stats.missing_embeddings === '0') {
        console.log('🎉 All guidelines now have embeddings!');
      } else {
        console.log(`⚠️  ${stats.missing_embeddings} guidelines still need embeddings`);
      }
      
      // Performance stats
      const totalTime = (Date.now() - this.startTime) / 1000;
      const avgTimePerGuideline = (totalTime / this.processedCount).toFixed(2);
      
      console.log('\n⏱️  PERFORMANCE STATS:');
      console.log(`Total processing time: ${totalTime.toFixed(1)}s`);
      console.log(`Guidelines processed: ${this.processedCount}`);
      console.log(`Average time per guideline: ${avgTimePerGuideline}s`);
      console.log(`Errors encountered: ${this.errorCount}`);
      
    } finally {
      await client.end();
    }
  }

  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// Main execution
async function main() {
  console.log('🚀 Starting OpenAI embedding generation process...');
  
  const generator = new EmbeddingGenerator();
  
  try {
    await generator.generateAllEmbeddings();
    console.log('🎉 Embedding generation completed successfully!');
  } catch (error) {
    console.error('💥 Embedding generation failed:', error);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main().catch(console.error);
}

export { EmbeddingGenerator };
