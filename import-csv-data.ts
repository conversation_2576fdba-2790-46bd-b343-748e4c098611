import { Client } from 'pg';
import * as fs from 'fs';
import * as path from 'path';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.development' });

// Initialize PostgreSQL client
const connectionString = process.env.POSTGRES_CONNECTION_STRING;

if (!connectionString) {
  console.error('❌ Missing PostgreSQL connection string. Please set POSTGRES_CONNECTION_STRING in .env.development');
  process.exit(1);
}

// Create a connection pool for database operations
const createClient = () => new Client({
  connectionString,
  ssl: { rejectUnauthorized: false }
});

interface CarrierData {
  carrier_name: string;
  payer_id: string | null;
  similar_name: string | null;
  claims_address: string | null;
  phone_number: string | null;
}

interface ProcedureData {
  procedure_code: string;
  description: string;
  category: string;
}

interface XRayRequirement {
  category: string;
  procedure: string;
  xray_needed: string;
}

class CSVImporter {
  private carrierIdMap: Map<string, number> = new Map();

  async importAllData() {
    console.log('🚀 Starting CSV data import...');
    
    try {
      // Step 1: Import insurance carriers
      await this.importCarriers();
      
      // Step 2: Import procedure codes
      await this.importProcedures();
      
      // Step 3: Import X-ray documentation requirements
      await this.importXRayRequirements();
      
      console.log('✅ All CSV data imported successfully!');
      
    } catch (error) {
      console.error('❌ Error importing CSV data:', error);
      throw error;
    }
  }

  private async importCarriers() {
    console.log('📊 Importing insurance carriers...');
    
    const csvPath = path.join(process.cwd(), 'assets', 'carrier-list.csv');
    const csvContent = fs.readFileSync(csvPath, 'utf-8');
    const lines = csvContent.split('\n').slice(1); // Skip header
    
    const carriers: CarrierData[] = [];
    const aliases: Array<{carrier_name: string, alias_name: string}> = [];
    
    for (const line of lines) {
      if (!line.trim()) continue;
      
      // Parse CSV line (handle commas in quoted fields)
      const fields = this.parseCSVLine(line);
      if (fields.length < 5) continue;
      
      const [insuranceCompany, payerId, similarName, claimsAddress, phoneNumber] = fields;
      
      if (!insuranceCompany || insuranceCompany === 'Insurance Company') continue;
      
      // Clean and normalize data
      const carrierData: CarrierData = {
        carrier_name: insuranceCompany.trim(),
        payer_id: payerId && payerId !== 'No Payer ID' ? payerId.trim() : null,
        similar_name: similarName && similarName.trim() ? similarName.trim() : null,
        claims_address: claimsAddress && claimsAddress.trim() ? claimsAddress.trim() : null,
        phone_number: phoneNumber && phoneNumber !== 'No Phone Number' ? this.normalizePhoneNumber(phoneNumber) : null
      };
      
      carriers.push(carrierData);
      
      // Create alias entries
      if (carrierData.similar_name && carrierData.similar_name !== carrierData.carrier_name) {
        aliases.push({
          carrier_name: carrierData.carrier_name,
          alias_name: carrierData.similar_name
        });
      }
    }
    
    console.log(`📝 Parsed ${carriers.length} carriers from CSV`);
    
    // Insert carriers in batches using PostgreSQL
    const client = createClient();
    await client.connect();

    try {
      const batchSize = 50;
      for (let i = 0; i < carriers.length; i += batchSize) {
        const batch = carriers.slice(i, i + batchSize);

        // Prepare batch insert query
        const values = batch.map(carrier => [
          carrier.carrier_name,
          this.determineCarrierType(carrier.carrier_name),
          carrier.payer_id,
          carrier.claims_address,
          carrier.phone_number,
          carrier.claims_address || carrier.phone_number ? JSON.stringify({
            claims_address: carrier.claims_address,
            phone_number: carrier.phone_number
          }) : null
        ]);

        const placeholders = values.map((_, idx) =>
          `($${idx * 6 + 1}, $${idx * 6 + 2}, $${idx * 6 + 3}, $${idx * 6 + 4}, $${idx * 6 + 5}, $${idx * 6 + 6})`
        ).join(', ');

        // First, check which carriers already exist
        const existingCarriers = new Set();
        const existingResult = await client.query('SELECT carrier_name FROM insurance_carriers');
        for (const row of existingResult.rows) {
          existingCarriers.add(row.carrier_name);
        }

        // Filter out existing carriers
        const newCarriers = batch.filter(carrier => !existingCarriers.has(carrier.carrier_name));

        if (newCarriers.length === 0) {
          console.log(`⏭️  Batch ${i / batchSize + 1}: All carriers already exist, skipping`);
          continue;
        }

        // Prepare insert for new carriers only
        const newValues = newCarriers.map(carrier => [
          carrier.carrier_name,
          this.determineCarrierType(carrier.carrier_name),
          carrier.payer_id,
          carrier.claims_address,
          carrier.phone_number,
          carrier.claims_address || carrier.phone_number ? JSON.stringify({
            claims_address: carrier.claims_address,
            phone_number: carrier.phone_number
          }) : null
        ]);

        const newPlaceholders = newValues.map((_, idx) =>
          `($${idx * 6 + 1}, $${idx * 6 + 2}, $${idx * 6 + 3}, $${idx * 6 + 4}, $${idx * 6 + 5}, $${idx * 6 + 6})`
        ).join(', ');

        const query = `
          INSERT INTO insurance_carriers (carrier_name, carrier_type, payer_id, claims_address, phone_number, contact_info)
          VALUES ${newPlaceholders}
          RETURNING id, carrier_name
        `;

        const flatValues = newValues.flat();
        const result = await client.query(query, flatValues);

        // Store carrier ID mapping for aliases
        for (const carrier of result.rows) {
          this.carrierIdMap.set(carrier.carrier_name, carrier.id);
        }

        console.log(`✅ Imported carrier batch ${i / batchSize + 1}/${Math.ceil(carriers.length / batchSize)}`);
      }
    } finally {
      await client.end();
    }
    
    // Import carrier aliases
    if (aliases.length > 0) {
      console.log(`📝 Importing ${aliases.length} carrier aliases...`);

      const aliasClient = createClient();
      await aliasClient.connect();

      try {
        // Check existing aliases to avoid duplicates
        const existingAliases = new Set();
        const existingResult = await aliasClient.query('SELECT carrier_id, alias_name FROM carrier_aliases');
        for (const row of existingResult.rows) {
          existingAliases.add(`${row.carrier_id}-${row.alias_name}`);
        }

        const aliasData = aliases
          .map(alias => {
            const carrierId = this.carrierIdMap.get(alias.carrier_name);
            if (!carrierId) return null;

            const key = `${carrierId}-${alias.alias_name}`;
            if (existingAliases.has(key)) return null;

            return [carrierId, alias.alias_name];
          })
          .filter(Boolean);

        if (aliasData.length > 0) {
          const placeholders = aliasData.map((_, idx) =>
            `($${idx * 2 + 1}, $${idx * 2 + 2})`
          ).join(', ');

          const query = `
            INSERT INTO carrier_aliases (carrier_id, alias_name)
            VALUES ${placeholders}
          `;

          const flatValues = aliasData.flat();
          await aliasClient.query(query, flatValues);
          console.log(`✅ Imported ${aliasData.length} new carrier aliases`);
        } else {
          console.log('⏭️  All aliases already exist, skipping');
        }
      } finally {
        await aliasClient.end();
      }
    }
    
    console.log('✅ Carrier import completed');
  }

  private async importProcedures() {
    console.log('🦷 Importing procedure codes...');
    
    const csvPath = path.join(process.cwd(), 'assets', 'insurance-codes.csv');
    const csvContent = fs.readFileSync(csvPath, 'utf-8');
    const lines = csvContent.split('\n').slice(2); // Skip header lines
    
    const procedures: ProcedureData[] = [];
    
    for (const line of lines) {
      if (!line.trim()) continue;
      
      const [code, description] = line.split(',').map(s => s.trim());
      if (!code || !description || code === 'Code') continue;
      
      procedures.push({
        procedure_code: code,
        description: description,
        category: this.determineProcedureCategory(code)
      });
    }
    
    console.log(`📝 Parsed ${procedures.length} procedures from CSV`);

    // Insert procedures using PostgreSQL
    const client = createClient();
    await client.connect();

    try {
      const values = procedures.map(proc => [proc.procedure_code, proc.description, proc.category]);
      const placeholders = values.map((_, idx) =>
        `($${idx * 3 + 1}, $${idx * 3 + 2}, $${idx * 3 + 3})`
      ).join(', ');

      // Check existing procedures
      const existingProcs = new Set();
      const existingResult = await client.query('SELECT procedure_code FROM procedures');
      for (const row of existingResult.rows) {
        existingProcs.add(row.procedure_code);
      }

      // Filter new procedures
      const newProcedures = procedures.filter(proc => !existingProcs.has(proc.procedure_code));

      if (newProcedures.length === 0) {
        console.log('⏭️  All procedures already exist, skipping');
        return;
      }

      const newValues = newProcedures.map(proc => [proc.procedure_code, proc.description, proc.category]);
      const newPlaceholders = newValues.map((_, idx) =>
        `($${idx * 3 + 1}, $${idx * 3 + 2}, $${idx * 3 + 3})`
      ).join(', ');

      const query = `
        INSERT INTO procedures (procedure_code, description, category)
        VALUES ${newPlaceholders}
        RETURNING id, procedure_code
      `;

      const flatValues = newValues.flat();
      const result = await client.query(query, flatValues);
      console.log(`✅ Imported ${result.rows.length} new procedures`);
    } finally {
      await client.end();
    }
  }

  private async importXRayRequirements() {
    console.log('📸 Importing X-ray documentation requirements...');
    
    const csvPath = path.join(process.cwd(), 'assets', 'insurance-resources-x-rays.csv');
    const csvContent = fs.readFileSync(csvPath, 'utf-8');
    const lines = csvContent.split('\n').slice(1); // Skip header
    
    const requirements: Array<{
      document_type: string;
      description: string;
      required_for: {
        category: string;
        procedure: string;
        procedure_type: string;
      };
      format_requirements: string;
    }> = [];
    
    for (const line of lines) {
      if (!line.trim()) continue;
      
      const fields = this.parseCSVLine(line);
      if (fields.length < 3) continue;
      
      const [category, procedure, xrayNeeded] = fields;
      if (!category || !procedure || category === 'CATEGORY') continue;
      
      if (xrayNeeded && xrayNeeded.trim() && !xrayNeeded.includes('NO X-RAY')) {
        requirements.push({
          document_type: 'X-Ray Documentation',
          description: `${procedure} - ${xrayNeeded}`,
          required_for: {
            category: category,
            procedure: procedure,
            procedure_type: this.mapCategoryToProcedureType(category)
          },
          format_requirements: xrayNeeded
        });
      }
    }
    
    console.log(`📝 Parsed ${requirements.length} X-ray requirements from CSV`);

    // Insert documentation requirements using PostgreSQL
    const client = createClient();
    await client.connect();

    try {
      if (requirements.length > 0) {
        const values = requirements.map(req => [
          req.document_type,
          req.description,
          JSON.stringify(req.required_for),
          req.format_requirements
        ]);

        const placeholders = values.map((_, idx) =>
          `($${idx * 4 + 1}, $${idx * 4 + 2}, $${idx * 4 + 3}, $${idx * 4 + 4})`
        ).join(', ');

        // Check for existing documentation requirements to avoid duplicates
        const existingDocs = new Set();
        const existingResult = await client.query('SELECT document_type, description FROM documentation_requirements');
        for (const row of existingResult.rows) {
          existingDocs.add(`${row.document_type}-${row.description}`);
        }

        // Filter out existing requirements
        const newRequirements = requirements.filter(req => {
          const key = `${req.document_type}-${req.description}`;
          return !existingDocs.has(key);
        });

        if (newRequirements.length === 0) {
          console.log('⏭️  All documentation requirements already exist, skipping');
          return;
        }

        const newValues = newRequirements.map(req => [
          req.document_type,
          req.description,
          JSON.stringify(req.required_for),
          req.format_requirements
        ]);

        const newPlaceholders = newValues.map((_, idx) =>
          `($${idx * 4 + 1}, $${idx * 4 + 2}, $${idx * 4 + 3}, $${idx * 4 + 4})`
        ).join(', ');

        const query = `
          INSERT INTO documentation_requirements (document_type, description, required_for, format_requirements)
          VALUES ${newPlaceholders}
          RETURNING id
        `;

        const flatValues = newValues.flat();
        const result = await client.query(query, flatValues);
        console.log(`✅ Imported ${result.rows.length} new documentation requirements`);
      }
    } finally {
      await client.end();
    }
  }

  // Helper methods
  private parseCSVLine(line: string): string[] {
    const result: string[] = [];
    let current = '';
    let inQuotes = false;
    
    for (let i = 0; i < line.length; i++) {
      const char = line[i];
      
      if (char === '"') {
        inQuotes = !inQuotes;
      } else if (char === ',' && !inQuotes) {
        result.push(current.trim());
        current = '';
      } else {
        current += char;
      }
    }
    
    result.push(current.trim());
    return result;
  }

  private normalizePhoneNumber(phone: string): string {
    // Remove all non-digit characters except parentheses and hyphens
    return phone.replace(/[^\d\-\(\)\s]/g, '').trim();
  }

  private determineCarrierType(carrierName: string): string {
    const name = carrierName.toLowerCase();

    // Check for Medicare Advantage plans
    if (name.includes('medicare advantage') || name.includes('medicare adv') ||
        (name.includes('medicare') && (name.includes('advantage') || name.includes('adv')))) {
      return 'Medicare Advantage';
    }

    // Check for major national carriers
    if (name.includes('blue cross') || name.includes('bcbs') || name.includes('anthem') ||
        name.includes('aetna') || name.includes('cigna') || name.includes('humana') ||
        name.includes('delta dental') || name.includes('metlife') || name.includes('guardian')) {
      return 'National';
    }

    // Check for TPA (Third Party Administrator) indicators
    if (name.includes('tpa') || name.includes('third party') || name.includes('administrator') ||
        name.includes('claims admin') || name.includes('benefits admin')) {
      return 'TPA';
    }

    // Everything else goes to 'Other'
    return 'Other';
  }

  private determineProcedureCategory(code: string): string {
    const codeNum = parseInt(code.substring(1));
    
    if (codeNum >= 100 && codeNum <= 999) return 'Diagnostic';
    if (codeNum >= 1000 && codeNum <= 1999) return 'Preventive';
    if (codeNum >= 2000 && codeNum <= 2999) return 'Restorative';
    if (codeNum >= 3000 && codeNum <= 3999) return 'Endodontic';
    if (codeNum >= 4000 && codeNum <= 4999) return 'Periodontal';
    if (codeNum >= 5000 && codeNum <= 5999) return 'Prosthodontic';
    if (codeNum >= 6000 && codeNum <= 6999) return 'Implant Services';
    if (codeNum >= 7000 && codeNum <= 7999) return 'Oral Surgery';
    if (codeNum >= 8000 && codeNum <= 8999) return 'Orthodontic';
    if (codeNum >= 9000 && codeNum <= 9999) return 'Adjunctive General Services';
    
    return 'General';
  }

  private mapCategoryToProcedureType(category: string): string {
    const categoryMap: { [key: string]: string } = {
      'Preventive Care': 'preventive',
      'Restorative Procedures': 'restorative',
      'Cosmetic Dentistry': 'cosmetic',
      'Endodontics': 'endodontic',
      'Periodontics': 'periodontal',
      'Orthodontics': 'orthodontic',
      'Oral Surgery': 'oral_surgery',
      'Pediatric Dentistry': 'pediatric',
      'Prosthodontics': 'prosthodontic',
      'Emergency Dental Services': 'emergency'
    };
    
    return categoryMap[category] || 'general';
  }
}

// Main execution
async function main() {
  console.log('🚀 Starting CSV data import process...');
  
  const importer = new CSVImporter();
  
  try {
    await importer.importAllData();
    console.log('🎉 CSV data import completed successfully!');
  } catch (error) {
    console.error('💥 CSV data import failed:', error);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main().catch(console.error);
}

export { CSVImporter };
