// Ensure we have valid API keys
import * as dotenv from 'dotenv';

// Load .env.development file
dotenv.config({ path: '.env.development' });

// Validate API key
if (!process.env.OPENAI_API_KEY) {
  console.error('ERROR: OPENAI_API_KEY is not set in .env.development');
  process.exit(1);
}

// Make sure it's available to openai library directly
process.env.AI_OPENAI_API_KEY = process.env.OPENAI_API_KEY;

import { mastra, initializeGuidelines } from './mastra';

async function main() {
  try {
    // Initialize the insurance guidelines database
    console.log('Initializing dental insurance guidelines database...');
    await initializeGuidelines();
    
    console.log('\nDental Narrator Agent is ready to use!');
    console.log('Use it to generate insurance claim narratives from chart notes.');
    console.log('Example prompt: "Generate a narrative for these dental chart notes for Delta Dental"');
    
    // Additional information about starting a server could go here
    console.log('\nYou can start the Mastra server with:');
    console.log('npx mastra dev');
    console.log('This will make your agent available at http://localhost:4111');
  } catch (error) {
    console.error('Error starting the application:', error);
  }
}

main(); 