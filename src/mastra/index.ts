import { <PERSON><PERSON> } from '@mastra/core';
import { createLogger } from '@mastra/core/logger';
import { weatherWorkflow } from './workflows';
import { dentalNarratorAgent, weatherAgent } from './agents';

export const mastra: Mastra = new Mastra({
  workflows: { },
  agents: { dentalNarratorAgent },
  logger: createLogger({
    name: '<PERSON><PERSON>',
    level: 'info',
  }),
});

// Let's create a function to initialize the insurance guidelines database
export const initializeGuidelines = async () => {
  try {
    const agent = mastra.getAgent('dentalNarratorAgent');
    // We need to manually run the tool in this function
    await agent.generate('Initialize the guidelines database', {
      toolChoice: {
        type: 'tool',
        toolName: 'initializeGuidelines',
      },
    });
    
    console.info('Guidelines database initialized successfully');
  } catch (error) {
    console.error('Error initializing guidelines database:', error);
  }
};
