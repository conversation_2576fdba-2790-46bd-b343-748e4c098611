import { createTool } from '@mastra/core/tools';
import { z } from 'zod';
import { openai } from '@ai-sdk/openai';
import { createVectorQueryTool } from '@mastra/rag';
import { MDocument } from '@mastra/rag';
import { PgVector } from '@mastra/pg';
import { embedMany, embed, generateText } from 'ai';

// Log the API key (masked) to help with debugging
const apiKey = process.env.OPENAI_API_KEY || '';
const maskedKey = apiKey.substring(0, 10) + '...' + apiKey.substring(apiKey.length - 5);
console.log(`OpenAI API Key (masked): ${maskedKey}`);
console.log(`API Key length: ${apiKey.length} characters`);

// Dental Insurance Guidelines Interface
interface InsuranceGuidelines {
  carrier: string;
  requirements: {
    general: string[];
    procedureSpecific?: Record<string, string[]>;
    characterLimits?: { min: number; max: number };
    requiredAttachments?: string[];
  };
}

// Enhanced insurance guidelines database for new schema
const insuranceGuidelinesDB: Record<string, InsuranceGuidelines> = {
  'default': {
    carrier: 'Default/General',
    requirements: {
      general: [
        'Include patient\'s chief complaint',
        'Clearly state the diagnosis',
        'Explain the medical necessity',
        'Justify the treatment provided',
        'Keep narrative between 500-1500 characters'
      ],
      characterLimits: { min: 500, max: 1500 }
    }
  },
  'delta': {
    carrier: 'Delta Dental',
    requirements: {
      general: [
        'Include patient\'s chief complaint',
        'Clearly state the diagnosis',
        'Explain the medical necessity',
        'Justify the treatment provided',
        'Reference specific CDT codes when applicable',
        'Keep narrative between 500-1200 characters'
      ],
      procedureSpecific: {
        'periodontal': [
          'Include pocket depth measurements',
          'Reference bone loss observed in radiographs',
          'Describe mobility if present'
        ],
        'crown': [
          'Specify extent of tooth destruction',
          'Document any existing restorations',
          'Explain why a less expensive restoration is not sufficient'
        ],
        'root canal': [
          'Document pulpal involvement',
          'Include symptoms of infection or pain',
          'Reference radiographic findings'
        ]
      },
      characterLimits: { min: 500, max: 1200 },
      requiredAttachments: [
        'Pre-operative X-rays for all crown and root canal procedures',
        'Periodontal charting for all periodontal procedures'
      ]
    }
  },
  'cigna': {
    carrier: 'Cigna Dental',
    requirements: {
      general: [
        'Include patient\'s chief complaint and symptoms',
        'Provide clear diagnosis with supporting findings',
        'Document medical necessity in detail',
        'Explain why the specific treatment was chosen',
        'Use appropriate dental terminology',
        'Keep narrative between 600-1500 characters'
      ],
      procedureSpecific: {
        'implant': [
          'Document bone quality and quantity',
          'Explain why other treatment options were not suitable',
          'Include relevant medical history affecting implant suitability'
        ],
        'orthodontic': [
          'Document functional impairments',
          'Include details on occlusal relationship',
          'Specify treatment duration expectations'
        ]
      },
      characterLimits: { min: 600, max: 1500 },
      requiredAttachments: [
        'Full mouth series for implant cases',
        'Cephalometric analysis for orthodontic cases',
        'Intraoral photos when available'
      ]
    }
  }
};

// Initialize PgVector for remote Supabase
const pgVector = new PgVector(process.env.POSTGRES_CONNECTION_STRING || 'postgresql://postgres:postgres@localhost:5432/postgres');

// Log the database connection string (with password masked)
const connectionString = process.env.POSTGRES_CONNECTION_STRING || '';
const maskedConnectionString = connectionString.replace(/:[^:@]+@/, ':****@');
console.log(`Initializing PgVector with connection: ${maskedConnectionString}`);

// Create the vector query tool for insurance guidelines
export const insuranceGuidelinesQueryTool = createVectorQueryTool({
  vectorStoreName: 'pgVector',
  indexName: 'insurance_guidelines',
  model: openai.embedding('text-embedding-3-small'),
  reranker: {
    model: openai('gpt-4o-mini'),
    options: {
      weights: {
        semantic: 0.6,
        vector: 0.3,
        position: 0.1
      },
      topK: 5
    }
  },
  description: 'Search insurance carrier guidelines for specific requirements related to dental procedures and claims'
});

// Tool to initialize and populate the remote Supabase database with insurance guidelines
export const initializeGuidelines = createTool({
  id: 'initialize-guidelines',
  description: 'Initialize and populate the remote Supabase database with insurance guidelines and vector embeddings',
  outputSchema: z.object({
    success: z.boolean(),
    message: z.string()
  }),
  execute: async () => {
    try {
      console.log('Starting guidelines initialization process for remote Supabase...');
      
      // First, populate insurance carriers in the new schema
      const carrierInserts = [];
      const carrierIdMap = new Map<string, number>();
      
      for (const [carrierId, carrierData] of Object.entries(insuranceGuidelinesDB)) {
        const carrierCode = carrierId.toUpperCase();
        carrierInserts.push({
          name: carrierData.carrier,
          code: carrierCode,
          created_at: new Date(),
          updated_at: new Date()
        });
      }
      
      // Insert carriers into remote database using PgVector
      const carrierResults = await pgVector.client.query(
        'INSERT INTO insurance_carriers (carrier_name, carrier_type, created_at, updated_at) VALUES ($1, $2, NOW(), NOW()) RETURNING id, carrier_name',
        ['Default/General', 'National']
      );
      carrierIdMap.set('default', carrierResults.rows[0].id);
      
      const deltaResult = await pgVector.client.query(
        'INSERT INTO insurance_carriers (carrier_name, carrier_type, created_at, updated_at) VALUES ($1, $2, NOW(), NOW()) RETURNING id, carrier_name',
        ['Delta Dental', 'National']
      );
      carrierIdMap.set('delta', deltaResult.rows[0].id);
      
      const cignaResult = await pgVector.client.query(
        'INSERT INTO insurance_carriers (carrier_name, carrier_type, created_at, updated_at) VALUES ($1, $2, NOW(), NOW()) RETURNING id, carrier_name',
        ['Cigna Dental', 'National']
      );
      carrierIdMap.set('cigna', cignaResult.rows[0].id);
      
      console.log('✅ Insurance carriers populated');
      
      // Create guideline documents for vector storage
      const guidelineDocuments: string[] = [];
      
      // Process each insurance carrier's guidelines
      for (const [carrierId, carrierData] of Object.entries(insuranceGuidelinesDB)) {
        const dbCarrierId = carrierIdMap.get(carrierId);
        if (!dbCarrierId) continue;
        
        // General guidelines
        guidelineDocuments.push(`Insurance Carrier: ${carrierData.carrier}\nGeneral Guidelines:\n${carrierData.requirements.general.join('\n')}`);
        
        // Character limits
        if (carrierData.requirements.characterLimits) {
          guidelineDocuments.push(
            `Insurance Carrier: ${carrierData.carrier}\nCharacter Limits:\nMinimum: ${carrierData.requirements.characterLimits.min}\nMaximum: ${carrierData.requirements.characterLimits.max}`
          );
        }
        
        // Required attachments
        if (carrierData.requirements.requiredAttachments) {
          guidelineDocuments.push(
            `Insurance Carrier: ${carrierData.carrier}\nRequired Attachments:\n${carrierData.requirements.requiredAttachments.join('\n')}`
          );
        }
        
        // Procedure-specific guidelines
        if (carrierData.requirements.procedureSpecific) {
          for (const [procedure, guidelines] of Object.entries(carrierData.requirements.procedureSpecific)) {
            guidelineDocuments.push(
              `Insurance Carrier: ${carrierData.carrier}\nProcedure: ${procedure}\nGuidelines:\n${guidelines.join('\n')}`
            );
          }
        }
      }
      
      // Create document objects
      const documents = guidelineDocuments.map(text => MDocument.fromText(text));
      
      // Chunk documents
      const allChunks = [];
      for (const doc of documents) {
        const chunks = await doc.chunk({
          strategy: 'recursive',
          size: 512,
          overlap: 50,
          separator: '\n',
        });
        allChunks.push(...chunks);
      }
      
      // Generate embeddings
      const { embeddings } = await embedMany({
        model: openai.embedding('text-embedding-3-small'),
        values: allChunks.map(chunk => chunk.text),
      });
      
      // Create index if it doesn't exist (for new embeddings table)
      console.log('Attempting to create vector index in remote Supabase database...');
      await pgVector.createIndex({
        indexName: 'insurance_guidelines',
        dimension: 1536, // Dimension for text-embedding-3-small
      }).catch((error) => {
        // Index may already exist, that's OK
        console.log('Index creation result:', error?.message || 'Index may already exist');
      });
      
      // Store embeddings in the new embeddings table
      console.log(`Storing ${embeddings.length} vectors in remote Supabase database...`);
      for (let i = 0; i < embeddings.length; i++) {
        const embedding = embeddings[i];
        const chunk = allChunks[i];
        
        await pgVector.client.query(
          'INSERT INTO embeddings (content_type, content_id, embedding, metadata, created_at, updated_at) VALUES ($1, $2, $3, $4, NOW(), NOW())',
          [
            'guideline',
            1, // Use 1 for guideline content since we don't have specific IDs
            `[${embedding.join(',')}]`,
            JSON.stringify({
              text: chunk.text,
              source: 'insurance_guidelines'
            })
          ]
        );
      }
      
      console.log('Successfully stored vectors in remote Supabase database');
      
      return {
        success: true,
        message: `Successfully initialized remote Supabase with ${carrierIdMap.size} carriers and ${allChunks.length} guideline chunks`
      };
    } catch (error) {
      return {
        success: false,
        message: `Failed to initialize guidelines: ${error instanceof Error ? error.message : String(error)}`
      };
    }
  }
});

// Enhanced dental narrator tool with improved processing
export const dentalNarratorTool = createTool({
  id: 'generate-dental-narrative',
  description: 'Generate insurance claim narratives from dental chart notes',
  inputSchema: z.object({
    chartNotes: z.string().describe('The dental chart notes to process'),
    insuranceCarrier: z.string().optional().describe('The insurance carrier name, e.g., Delta Dental, Cigna'),
    procedureCategory: z.string().optional().describe('The category of dental procedure, e.g., periodontal, crown, implant')
  }),
  outputSchema: z.object({
    narrative: z.string().describe('The generated insurance claim narrative'),
    characterCount: z.number().describe('Character count of the generated narrative'),
    requiredAttachments: z.array(z.string()).optional().describe('List of required attachments for the claim'),
    guidelines: z.array(z.string()).describe('The guidelines that were followed in creating the narrative'),
    withinLimits: z.boolean().describe('Whether the narrative is within character limits'),
    medicalHistory: z.string().optional().describe('Relevant medical history extracted from chart notes')
  }),
  execute: async ({ context }) => {
    const insuranceCarrier = context.insuranceCarrier || 'default';
    const procedureCategory = context.procedureCategory || '';
    const chartNotes = context.chartNotes;
    
    try {
      // Step 1: Retrieve insurance guidelines using RAG
      // Create a query combining carrier and procedure
      const guidelineQuery = `Insurance guidelines for ${insuranceCarrier} ${procedureCategory}`;
      
      // Generate embedding for the query
      const { embedding: queryEmbedding } = await embed({
        value: guidelineQuery,
        model: openai.embedding('text-embedding-3-small'),
      });
      
      // Query the vector database using the new embeddings table
      console.log(`Querying remote Supabase database for guidelines related to: "${guidelineQuery}"`);
      
      // For now, do text-based search on metadata until embeddings are properly generated
      const textSearchQuery = `
        SELECT metadata->>'text' as text, 
               metadata->>'carrier' as carrier,
               metadata->>'category' as category
        FROM embeddings 
        WHERE content_type = 'guideline'
        AND (
          metadata->>'text' ILIKE $1 
          OR metadata->>'carrier' ILIKE $2
          OR metadata->>'category' ILIKE $3
        )
        LIMIT 5
      `;
      
      const searchTerm = `%${insuranceCarrier}%`;
      const categoryTerm = `%${procedureCategory}%`;
      
      const queryResults = await pgVector.client.query(textSearchQuery, [searchTerm, searchTerm, categoryTerm]);
      console.log(`Retrieved ${queryResults.rows.length} results from remote Supabase database`);
      
      // Extract guidelines from results
      const relevantGuidelines = queryResults.rows.map(row => row.text).filter(Boolean);
      
      // Combine guidelines into a single text
      const guidelinesText = relevantGuidelines.join('\n\n');
      
      // Step 2: Process chart notes to extract key information
      // Use OpenAI to extract information from chart notes
      const extractionPrompt = `
From the following dental chart notes, extract:
1. Patient's chief complaints
2. Diagnosis that aligns with the complaints
3. Evidence of medical necessity
4. Treatment justification
5. Relevant medical history (if any)

Dental Chart Notes:
${chartNotes}

Format your response as JSON with the following fields:
{
  "complaints": "summary of chief complaints",
  "diagnosis": "diagnosis that aligns with complaints",
  "medicalNecessity": "detailed explanation of medical necessity",
  "treatmentJustification": "explicit justification for the treatment",
  "medicalHistory": "relevant medical history from the notes or 'None' if not present"
}
`;

      const extractionResponse = await generateText({
        model: openai('gpt-4o-mini'),
        prompt: extractionPrompt
      });
      
      // Parse the extracted information
      const extractedText = extractionResponse.text;
      let extractedInfo;
      
      try {
        // Extract JSON from the response
        const jsonMatch = extractedText.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          extractedInfo = JSON.parse(jsonMatch[0]);
        } else {
          throw new Error("Could not extract JSON from response");
        }
      } catch (error) {
        // Fallback if JSON parsing fails
        extractedInfo = {
          complaints: "Patient's chief complaints could not be extracted",
          diagnosis: "Diagnosis could not be extracted",
          medicalNecessity: "Medical necessity could not be determined",
          treatmentJustification: "Treatment justification could not be extracted",
          medicalHistory: "None"
        };
      }
      
      // Step 3: Generate narrative based on extracted info and guidelines
      const narrativePrompt = `
You are generating an insurance claim narrative for a dental procedure.

INSURANCE GUIDELINES:
${guidelinesText}

EXTRACTED PATIENT INFORMATION:
- Chief Complaints: ${extractedInfo.complaints}
- Diagnosis: ${extractedInfo.diagnosis}
- Medical Necessity: ${extractedInfo.medicalNecessity}
- Treatment Justification: ${extractedInfo.treatmentJustification}
- Medical History: ${extractedInfo.medicalHistory}

INSTRUCTIONS:
1. Create a concise, professional narrative that explicitly addresses all requirements.
2. ENSURE the narrative explicitly states treatment justification.
3. Reference specific insurance guidelines where applicable.
4. Include relevant medical history if significant.
5. For multiple procedures, address each one clearly.
6. Keep the narrative between ${getCharacterLimits(insuranceCarrier).min} and ${getCharacterLimits(insuranceCarrier).max} characters.
7. Use clear, precise language aligned with insurance standards.

Generate the narrative now:
`;

      const narrativeResponse = await generateText({
        model: openai('gpt-4o-mini'),
        prompt: narrativePrompt
      });
      const generatedNarrative = narrativeResponse.text.trim();
      
      // Check character limits
      const characterCount = generatedNarrative.length;
      const limits = getCharacterLimits(insuranceCarrier);
      const withinLimits = characterCount >= limits.min && characterCount <= limits.max;
      
      // If not within limits, adjust the narrative
      let finalNarrative = generatedNarrative;
      if (!withinLimits) {
        if (characterCount < limits.min) {
          // Narrative is too short, expand it
          const expandPrompt = `
The following insurance narrative is too short (${characterCount} characters). 
Please expand it to meet the minimum requirement of ${limits.min} characters
while maintaining the same core information and professional tone.
Do not add fictional information, but elaborate on existing points.

Narrative to expand:
${generatedNarrative}
`;
          const expandResponse = await generateText({
            model: openai('gpt-4o-mini'),
            prompt: expandPrompt
          });
          finalNarrative = expandResponse.text.trim();
        } else if (characterCount > limits.max) {
          // Narrative is too long, shorten it
          const shortenPrompt = `
The following insurance narrative is too long (${characterCount} characters).
Please shorten it to meet the maximum requirement of ${limits.max} characters
while preserving all essential information, especially treatment justification.

Narrative to shorten:
${generatedNarrative}
`;
          const shortenResponse = await generateText({
            model: openai('gpt-4o-mini'),
            prompt: shortenPrompt
          });
          finalNarrative = shortenResponse.text.trim();
        }
      }
      
      // Get required attachments
      const requiredAttachments = getRequiredAttachments(insuranceCarrier, procedureCategory);
      
      // Return the final result
      return {
        narrative: finalNarrative,
        characterCount: finalNarrative.length,
        requiredAttachments,
        guidelines: relevantGuidelines,
        withinLimits: finalNarrative.length >= limits.min && finalNarrative.length <= limits.max,
        medicalHistory: extractedInfo.medicalHistory !== 'None' ? extractedInfo.medicalHistory : undefined
      };
    } catch (error) {
      // Handle errors
      console.error('Error generating narrative:', error);
      
      // Return fallback response
      return {
        narrative: "Error generating narrative. Please try again with more detailed chart notes.",
        characterCount: 0,
        requiredAttachments: [],
        guidelines: [],
        withinLimits: false
      };
    }
  }
});

// Helper function to get character limits for a carrier
function getCharacterLimits(carrier: string): { min: number, max: number } {
  const normalizedCarrier = carrier.toLowerCase();
  let limits = { min: 500, max: 1500 }; // Default limits
  
  // Check for specific carrier limits
  for (const [id, data] of Object.entries(insuranceGuidelinesDB)) {
    if (normalizedCarrier.includes(id) && data.requirements.characterLimits) {
      limits = data.requirements.characterLimits;
      break;
    }
  }
  
  return limits;
}

// Helper function to get required attachments
function getRequiredAttachments(carrier: string, procedure: string): string[] {
  const normalizedCarrier = carrier.toLowerCase();
  const normalizedProcedure = procedure.toLowerCase();
  let attachments: string[] = [];
  
  // Find matching carrier
  for (const [id, data] of Object.entries(insuranceGuidelinesDB)) {
    if (normalizedCarrier.includes(id) && data.requirements.requiredAttachments) {
      // Add all carrier attachments
      attachments = [...data.requirements.requiredAttachments];
      
      // Filter for procedure-specific attachments if applicable
      if (normalizedProcedure && attachments.length > 0) {
        attachments = attachments.filter(attachment => 
          attachment.toLowerCase().includes(normalizedProcedure) ||
          !attachment.toLowerCase().includes('for all')
        );
      }
      
      break;
    }
  }
  
  return attachments;
}

// Empty export to maintain compatibility
export const weatherTool = {} as any;
