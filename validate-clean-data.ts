import { Client } from 'pg';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.development' });

// Initialize PostgreSQL client
const connectionString = process.env.POSTGRES_CONNECTION_STRING;

if (!connectionString) {
  console.error('❌ Missing PostgreSQL connection string. Please set POSTGRES_CONNECTION_STRING in .env.development');
  process.exit(1);
}

const createClient = () => new Client({
  connectionString,
  ssl: { rejectUnauthorized: false }
});

interface ValidationIssue {
  table: string;
  issue_type: string;
  description: string;
  count: number;
  sample_data?: any[];
}

interface CleaningAction {
  table: string;
  action: string;
  description: string;
  affected_rows: number;
}

class DataValidator {
  private issues: ValidationIssue[] = [];
  private cleaningActions: CleaningAction[] = [];

  async validateAndCleanAllData() {
    console.log('🔍 Starting comprehensive data validation and cleaning...');
    
    try {
      // Step 1: Validate insurance carriers
      await this.validateInsuranceCarriers();
      
      // Step 2: Validate procedures
      await this.validateProcedures();
      
      // Step 3: Validate documentation requirements
      await this.validateDocumentationRequirements();
      
      // Step 4: Check for cross-table consistency
      await this.validateCrossTableConsistency();
      
      // Step 5: Generate validation report
      this.generateValidationReport();
      
      // Step 6: Perform cleaning actions
      await this.performCleaningActions();
      
      console.log('✅ Data validation and cleaning completed!');
      
    } catch (error) {
      console.error('❌ Error during validation and cleaning:', error);
      throw error;
    }
  }

  private async validateInsuranceCarriers() {
    console.log('🏥 Validating insurance carriers...');
    const client = createClient();
    await client.connect();
    
    try {
      // Check for duplicate carrier names
      const duplicateQuery = `
        SELECT carrier_name, COUNT(*) as count 
        FROM insurance_carriers 
        GROUP BY carrier_name 
        HAVING COUNT(*) > 1
      `;
      const duplicates = await client.query(duplicateQuery);
      
      if (duplicates.rows.length > 0) {
        this.issues.push({
          table: 'insurance_carriers',
          issue_type: 'duplicates',
          description: 'Duplicate carrier names found',
          count: duplicates.rows.length,
          sample_data: duplicates.rows.slice(0, 5)
        });
      }
      
      // Check for invalid phone numbers
      const invalidPhoneQuery = `
        SELECT id, carrier_name, phone_number 
        FROM insurance_carriers 
        WHERE phone_number IS NOT NULL 
        AND phone_number != ''
        AND phone_number !~ '^[\\(\\)\\d\\s\\-\\+\\.]+$'
      `;
      const invalidPhones = await client.query(invalidPhoneQuery);
      
      if (invalidPhones.rows.length > 0) {
        this.issues.push({
          table: 'insurance_carriers',
          issue_type: 'invalid_phone',
          description: 'Invalid phone number formats',
          count: invalidPhones.rows.length,
          sample_data: invalidPhones.rows.slice(0, 5)
        });
      }
      
      // Check for missing essential data
      const missingDataQuery = `
        SELECT 
          COUNT(*) FILTER (WHERE carrier_name IS NULL OR carrier_name = '') as missing_names,
          COUNT(*) FILTER (WHERE carrier_type IS NULL OR carrier_type = '') as missing_types,
          COUNT(*) FILTER (WHERE phone_number IS NULL OR phone_number = '') as missing_phones,
          COUNT(*) FILTER (WHERE claims_address IS NULL OR claims_address = '') as missing_addresses
        FROM insurance_carriers
      `;
      const missingData = await client.query(missingDataQuery);
      const missing = missingData.rows[0];
      
      if (missing.missing_names > 0) {
        this.issues.push({
          table: 'insurance_carriers',
          issue_type: 'missing_data',
          description: 'Missing carrier names',
          count: parseInt(missing.missing_names)
        });
      }
      
      if (missing.missing_phones > 0) {
        this.issues.push({
          table: 'insurance_carriers',
          issue_type: 'missing_data',
          description: 'Missing phone numbers',
          count: parseInt(missing.missing_phones)
        });
      }
      
      // Check for inconsistent carrier types
      const carrierTypesQuery = `
        SELECT carrier_type, COUNT(*) as count 
        FROM insurance_carriers 
        GROUP BY carrier_type 
        ORDER BY count DESC
      `;
      const carrierTypes = await client.query(carrierTypesQuery);
      console.log('📊 Carrier type distribution:', carrierTypes.rows);
      
      // Check for very long carrier names (potential data issues)
      const longNamesQuery = `
        SELECT id, carrier_name, LENGTH(carrier_name) as name_length
        FROM insurance_carriers 
        WHERE LENGTH(carrier_name) > 100
      `;
      const longNames = await client.query(longNamesQuery);
      
      if (longNames.rows.length > 0) {
        this.issues.push({
          table: 'insurance_carriers',
          issue_type: 'data_quality',
          description: 'Unusually long carrier names (>100 chars)',
          count: longNames.rows.length,
          sample_data: longNames.rows.slice(0, 3)
        });
      }
      
    } finally {
      await client.end();
    }
  }

  private async validateProcedures() {
    console.log('🦷 Validating procedures...');
    const client = createClient();
    await client.connect();
    
    try {
      // Check for duplicate procedure codes
      const duplicateQuery = `
        SELECT procedure_code, COUNT(*) as count 
        FROM procedures 
        GROUP BY procedure_code 
        HAVING COUNT(*) > 1
      `;
      const duplicates = await client.query(duplicateQuery);
      
      if (duplicates.rows.length > 0) {
        this.issues.push({
          table: 'procedures',
          issue_type: 'duplicates',
          description: 'Duplicate procedure codes',
          count: duplicates.rows.length,
          sample_data: duplicates.rows
        });
      }
      
      // Check for invalid CDT codes (should start with D and have 4 digits)
      const invalidCodesQuery = `
        SELECT id, procedure_code, description 
        FROM procedures 
        WHERE procedure_code !~ '^D\\d{4}$'
      `;
      const invalidCodes = await client.query(invalidCodesQuery);
      
      if (invalidCodes.rows.length > 0) {
        this.issues.push({
          table: 'procedures',
          issue_type: 'invalid_format',
          description: 'Invalid CDT code format (should be D####)',
          count: invalidCodes.rows.length,
          sample_data: invalidCodes.rows
        });
      }
      
      // Check for missing descriptions
      const missingDescQuery = `
        SELECT id, procedure_code 
        FROM procedures 
        WHERE description IS NULL OR description = '' OR LENGTH(description) < 3
      `;
      const missingDesc = await client.query(missingDescQuery);
      
      if (missingDesc.rows.length > 0) {
        this.issues.push({
          table: 'procedures',
          issue_type: 'missing_data',
          description: 'Missing or very short procedure descriptions',
          count: missingDesc.rows.length,
          sample_data: missingDesc.rows
        });
      }
      
      // Check category distribution
      const categoryQuery = `
        SELECT category, COUNT(*) as count 
        FROM procedures 
        GROUP BY category 
        ORDER BY count DESC
      `;
      const categories = await client.query(categoryQuery);
      console.log('📊 Procedure category distribution:', categories.rows);
      
    } finally {
      await client.end();
    }
  }

  private async validateDocumentationRequirements() {
    console.log('📋 Validating documentation requirements...');
    const client = createClient();
    await client.connect();
    
    try {
      // Check for duplicate requirements
      const duplicateQuery = `
        SELECT document_type, description, COUNT(*) as count 
        FROM documentation_requirements 
        GROUP BY document_type, description 
        HAVING COUNT(*) > 1
      `;
      const duplicates = await client.query(duplicateQuery);
      
      if (duplicates.rows.length > 0) {
        this.issues.push({
          table: 'documentation_requirements',
          issue_type: 'duplicates',
          description: 'Duplicate documentation requirements',
          count: duplicates.rows.length,
          sample_data: duplicates.rows
        });
      }
      
      // Check for invalid JSON in required_for field
      const invalidJsonQuery = `
        SELECT id, description, required_for 
        FROM documentation_requirements 
        WHERE required_for IS NOT NULL 
        AND NOT (required_for::text)::json IS NOT NULL
      `;
      
      try {
        const invalidJson = await client.query(invalidJsonQuery);
        if (invalidJson.rows.length > 0) {
          this.issues.push({
            table: 'documentation_requirements',
            issue_type: 'invalid_json',
            description: 'Invalid JSON in required_for field',
            count: invalidJson.rows.length,
            sample_data: invalidJson.rows.slice(0, 3)
          });
        }
      } catch (error) {
        console.log('⚠️  Could not validate JSON format - may need manual review');
      }
      
      // Check for missing essential fields
      const missingDataQuery = `
        SELECT 
          COUNT(*) FILTER (WHERE document_type IS NULL OR document_type = '') as missing_type,
          COUNT(*) FILTER (WHERE description IS NULL OR description = '') as missing_desc,
          COUNT(*) FILTER (WHERE format_requirements IS NULL OR format_requirements = '') as missing_format
        FROM documentation_requirements
      `;
      const missingData = await client.query(missingDataQuery);
      const missing = missingData.rows[0];
      
      if (missing.missing_type > 0 || missing.missing_desc > 0 || missing.missing_format > 0) {
        this.issues.push({
          table: 'documentation_requirements',
          issue_type: 'missing_data',
          description: `Missing data: ${missing.missing_type} types, ${missing.missing_desc} descriptions, ${missing.missing_format} formats`,
          count: parseInt(missing.missing_type) + parseInt(missing.missing_desc) + parseInt(missing.missing_format)
        });
      }
      
    } finally {
      await client.end();
    }
  }

  private async validateCrossTableConsistency() {
    console.log('🔗 Validating cross-table consistency...');
    const client = createClient();
    await client.connect();
    
    try {
      // Check if carrier_aliases references valid carriers
      const orphanAliasesQuery = `
        SELECT ca.id, ca.alias_name, ca.carrier_id
        FROM carrier_aliases ca
        LEFT JOIN insurance_carriers ic ON ca.carrier_id = ic.id
        WHERE ic.id IS NULL
      `;
      const orphanAliases = await client.query(orphanAliasesQuery);
      
      if (orphanAliases.rows.length > 0) {
        this.issues.push({
          table: 'carrier_aliases',
          issue_type: 'orphan_records',
          description: 'Carrier aliases referencing non-existent carriers',
          count: orphanAliases.rows.length,
          sample_data: orphanAliases.rows
        });
      }
      
      // Check for potential carrier name variations that should be aliases
      const similarNamesQuery = `
        SELECT c1.carrier_name as name1, c2.carrier_name as name2
        FROM insurance_carriers c1
        JOIN insurance_carriers c2 ON c1.id < c2.id
        WHERE SIMILARITY(c1.carrier_name, c2.carrier_name) > 0.7
        AND c1.carrier_name != c2.carrier_name
      `;
      
      try {
        const similarNames = await client.query(similarNamesQuery);
        if (similarNames.rows.length > 0) {
          this.issues.push({
            table: 'insurance_carriers',
            issue_type: 'potential_duplicates',
            description: 'Carriers with very similar names (potential duplicates or missing aliases)',
            count: similarNames.rows.length,
            sample_data: similarNames.rows.slice(0, 5)
          });
        }
      } catch (error) {
        console.log('⚠️  Could not check name similarity - pg_trgm extension may not be available');
      }
      
    } finally {
      await client.end();
    }
  }

  private generateValidationReport() {
    console.log('\n📋 DATA VALIDATION REPORT');
    console.log('=' .repeat(50));
    
    if (this.issues.length === 0) {
      console.log('✅ No data quality issues found! Data is clean.');
      return;
    }
    
    console.log(`Found ${this.issues.length} data quality issues:\n`);
    
    this.issues.forEach((issue, index) => {
      console.log(`${index + 1}. ${issue.table.toUpperCase()} - ${issue.issue_type.toUpperCase()}`);
      console.log(`   Description: ${issue.description}`);
      console.log(`   Count: ${issue.count}`);
      
      if (issue.sample_data && issue.sample_data.length > 0) {
        console.log('   Sample data:');
        issue.sample_data.forEach(sample => {
          console.log(`   - ${JSON.stringify(sample)}`);
        });
      }
      console.log('');
    });
  }

  private async performCleaningActions() {
    console.log('🧹 Performing data cleaning actions...');
    
    // For now, we'll implement basic cleaning actions
    // More complex cleaning would require business rules and user approval
    
    const client = createClient();
    await client.connect();
    
    try {
      // Clean phone numbers - normalize format
      const phoneCleanQuery = `
        UPDATE insurance_carriers 
        SET phone_number = REGEXP_REPLACE(
          REGEXP_REPLACE(phone_number, '[^0-9\\(\\)\\-\\s\\+\\.]', '', 'g'),
          '\\s+', ' ', 'g'
        )
        WHERE phone_number IS NOT NULL 
        AND phone_number != ''
        AND phone_number ~ '[^0-9\\(\\)\\-\\s\\+\\.]'
      `;
      
      const phoneResult = await client.query(phoneCleanQuery);
      if (phoneResult.rowCount && phoneResult.rowCount > 0) {
        this.cleaningActions.push({
          table: 'insurance_carriers',
          action: 'normalize_phone_numbers',
          description: 'Normalized phone number formats by removing invalid characters',
          affected_rows: phoneResult.rowCount
        });
      }
      
      // Trim whitespace from carrier names
      const trimNamesQuery = `
        UPDATE insurance_carriers 
        SET carrier_name = TRIM(carrier_name)
        WHERE carrier_name != TRIM(carrier_name)
      `;
      
      const trimResult = await client.query(trimNamesQuery);
      if (trimResult.rowCount && trimResult.rowCount > 0) {
        this.cleaningActions.push({
          table: 'insurance_carriers',
          action: 'trim_carrier_names',
          description: 'Trimmed whitespace from carrier names',
          affected_rows: trimResult.rowCount
        });
      }
      
      // Standardize procedure descriptions (capitalize first letter)
      const capitalizeDescQuery = `
        UPDATE procedures 
        SET description = INITCAP(description)
        WHERE description != INITCAP(description)
        AND description IS NOT NULL
      `;
      
      const capitalizeResult = await client.query(capitalizeDescQuery);
      if (capitalizeResult.rowCount && capitalizeResult.rowCount > 0) {
        this.cleaningActions.push({
          table: 'procedures',
          action: 'capitalize_descriptions',
          description: 'Standardized procedure description capitalization',
          affected_rows: capitalizeResult.rowCount
        });
      }
      
    } finally {
      await client.end();
    }
    
    // Generate cleaning report
    if (this.cleaningActions.length > 0) {
      console.log('\n🧹 DATA CLEANING ACTIONS PERFORMED');
      console.log('=' .repeat(50));
      
      this.cleaningActions.forEach((action, index) => {
        console.log(`${index + 1}. ${action.table.toUpperCase()} - ${action.action.toUpperCase()}`);
        console.log(`   Description: ${action.description}`);
        console.log(`   Affected rows: ${action.affected_rows}`);
        console.log('');
      });
    } else {
      console.log('✅ No cleaning actions needed - data is already clean!');
    }
  }
}

// Main execution
async function main() {
  console.log('🚀 Starting data validation and cleaning process...');
  
  const validator = new DataValidator();
  
  try {
    await validator.validateAndCleanAllData();
    console.log('🎉 Data validation and cleaning completed successfully!');
  } catch (error) {
    console.error('💥 Data validation and cleaning failed:', error);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main().catch(console.error);
}

export { DataValidator };
